import React, { useEffect, useState, useMemo } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView, BackHandler, Alert, FlatList, Modal } from 'react-native';
import { useAppContext } from '../context/AppContext';
import { getExtrato, ExtratoResponse, ExtratoItem } from '../services/api';
import Footer from '../components/Footer';
import LoadingSpinner from '../components/LoadingSpinner';
import SafeHeader from '../components/SafeHeader';
import Cores from '../constants/Cores';
import EstilosComuns from '../constants/Estilos';
import { getSafeBottomPadding } from '../utils/safeArea';

interface ExtratoScreenProps {
  navigation: {
    navigate: (screen: 'Login' | 'Home' | 'Saldo' | 'Extrato') => void;
  };
}

interface MesOption {
  label: string;
  value: string;
  mes: string;
  ano: string;
}

export default function ExtratoScreen({ navigation }: ExtratoScreenProps) {
  const { getUsuarioData } = useAppContext();
  const [extratoData, setExtratoData] = useState<ExtratoResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [mesesDisponiveis, setMesesDisponiveis] = useState<MesOption[]>([]);
  const [mesSelecionado, setMesSelecionado] = useState<string>('');
  const [showMesSelector, setShowMesSelector] = useState(false);
  const [tooltipVisible, setTooltipVisible] = useState(false);

  // Converter número do mês para nome
  const getMesNome = (mes: number): string => {
    const nomes = ['', 'Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun', 'Jul', 'Ago', 'Set', 'Out', 'Nov', 'Dez'];
    return nomes[mes] || '';
  };
  const [tooltipText, setTooltipText] = useState('');

  // Safe area padding para total
  const safeBottomPadding = getSafeBottomPadding(10);

  // Controlar o botão de voltar
  useEffect(() => {
    const backAction = () => {
      navigation.navigate('Home');
      return true;
    };

    const backHandler = BackHandler.addEventListener('hardwareBackPress', backAction);
    return () => backHandler.remove();
  }, [navigation]);

  // Carregar dados do extrato
  useEffect(() => {
    loadExtratoData();
  }, []);

  const loadExtratoData = async (mesform?: string, anoform?: string) => {
    const usuarioData = getUsuarioData();
    
    if (!usuarioData) {
      Alert.alert('Erro', 'Dados do usuário não encontrados. Faça login novamente.');
      navigation.navigate('Login');
      return;
    }

    setLoading(true);
    
    try {
      const response = await getExtrato(
        usuarioData.usuario.codass,
        usuarioData.usuario.codent,
        mesform,
        anoform
      );

      if (response.success && response.data) {
        setExtratoData(response.data);

        // Formatar o mês selecionado para exibição (Mês/AA)
        const [mes, ano] = response.data.mesSelecionado.split('/');
        const mesNome = getMesNome(parseInt(mes));
        const anoAbreviado = ano.slice(-2);
        setMesSelecionado(`${mesNome}/${anoAbreviado}`);

        // Gerar lista de meses disponíveis apenas se há dados válidos
        if (response.data && response.data.mesAtual) {
          generateMesesDisponiveis(response.data);
        } else {
          console.log('⚠️ Dados insuficientes para gerar lista de meses - usando mês atual apenas');
          // Criar lista com apenas o mês atual
          const mesAtual = parseInt(response.data.mes);
          const anoAtual = parseInt(response.data.ano);
          const mesNomeAtual = getMesNome(mesAtual);
          const anoAbreviadoAtual = anoAtual.toString().slice(-2);

          setMesesDisponiveis([{
            label: `${mesNomeAtual}/${anoAbreviadoAtual}`,
            value: `${mesAtual}/${anoAtual}`,
            mes: mesAtual.toString().padStart(2, '0'),
            ano: anoAtual.toString(),
          }]);
        }
      } else {
        Alert.alert('Erro', response.message);
      }
    } catch (error) {
      console.error('Erro ao carregar extrato:', error);
      Alert.alert('Erro', 'Falha ao carregar extrato.');
    } finally {
      setLoading(false);
    }
  };

  // Gerar lista de meses disponíveis (12 meses para trás + todos para frente)
  const generateMesesDisponiveis = (data: ExtratoResponse) => {
    try {
      const meses: MesOption[] = [];
      const mesUniversalAtual = data.mesUniversalAtual;

      // Verificar se mesAtual existe (correção do erro)
      if (!data.mesAtual) {
        console.log('⚠️ mesAtual não encontrado - extrato null = apenas 12 meses para trás');
        console.log('📊 Dados base:', { mes: data.mes, ano: data.ano, mesUniversalAtual });
        // Fallback: gerar apenas 12 meses para trás (incluindo o atual)

        // Gerar 12 meses para trás (incluindo o mês atual)
        for (let i = 11; i >= 0; i--) {
          const mesUniversal = mesUniversalAtual - i;
          const diferenca = mesUniversal - mesUniversalAtual;

          let mes = parseInt(data.mes);
          let ano = parseInt(data.ano);

          mes += diferenca;

          while (mes < 1) {
            mes += 12;
            ano -= 1;
          }

          const mesNome = getMesNome(mes);
          const anoAbreviado = ano.toString().slice(-2);

          console.log(`📅 Mês gerado (${i} meses atrás):`, { mes, ano, mesNome, diferenca });

          meses.push({
            label: `${mesNome}/${anoAbreviado}`,
            value: `${mes}/${ano}`,
            mes: mes.toString().padStart(2, '0'),
            ano: ano.toString(),
          });
        }

        // Ordenar os meses do mais recente para o mais antigo
        meses.reverse();

        console.log('📅 Meses gerados (apenas para trás):', meses.map(m => m.label));

        setMesesDisponiveis(meses);
        return;
      }

      // Verificar se há último consumo válido
      const temUltimoConsumoValido = data.ultimoConsumo &&
                                    data.ultimoConsumo.mesUniversal !== null &&
                                    data.ultimoConsumo.mesUniversal !== undefined;

      let mesUniversalUltimo: number;

      if (temUltimoConsumoValido) {
        mesUniversalUltimo = parseInt(data.ultimoConsumo.mesUniversal.toString());
        console.log('✅ Último consumo válido encontrado:', mesUniversalUltimo);
      } else {
        // Se não há último consumo, usar o mês atual como referência
        mesUniversalUltimo = mesUniversalAtual;
        console.log('⚠️ Último consumo inválido - usando mês atual como referência:', mesUniversalUltimo);
      }

    // Começar 12 meses antes do atual
    const mesUniversalInicio = mesUniversalAtual - 12;

    // Determinar o fim: se há último consumo válido, usar ele, senão usar mês atual + 12 meses
    const mesUniversalFim = temUltimoConsumoValido ? mesUniversalUltimo : mesUniversalAtual + 12;

    console.log('📅 Gerando meses:', {
      inicio: mesUniversalInicio,
      fim: mesUniversalFim,
      atual: mesUniversalAtual,
      temUltimoConsumo: temUltimoConsumoValido
    });

    // Gerar meses de 12 meses atrás até o fim determinado
    for (let mesUniversal = mesUniversalInicio; mesUniversal <= mesUniversalFim; mesUniversal++) {
      // Calcular mês e ano baseado no mesUniversal
      const diferenca = mesUniversal - mesUniversalAtual;

      let mes = parseInt(data.mes);
      let ano = parseInt(data.ano);

      // Adicionar/subtrair a diferença de meses
      mes += diferenca;

      // Ajustar ano se necessário
      while (mes > 12) {
        mes -= 12;
        ano += 1;
      }
      while (mes < 1) {
        mes += 12;
        ano -= 1;
      }

      const mesNome = getMesNome(mes);
      const anoAbreviado = ano.toString().slice(-2);

      meses.push({
        label: `${mesNome}/${anoAbreviado}`,
        value: `${mes}/${ano}`,
        mes: mes.toString().padStart(2, '0'),
        ano: ano.toString(),
      });
    }

      // Ordenar os meses do mais recente para o mais antigo
      meses.reverse();

      setMesesDisponiveis(meses);
    } catch (error) {
      console.error('❌ Erro ao gerar meses disponíveis:', error);
      // Fallback: criar lista com apenas o mês atual
      const mesAtual = parseInt(data.mes);
      const anoAtual = parseInt(data.ano);
      const mesNomeAtual = getMesNome(mesAtual);
      const anoAbreviadoAtual = anoAtual.toString().slice(-2);

      setMesesDisponiveis([{
        label: `${mesNomeAtual}/${anoAbreviadoAtual}`,
        value: `${mesAtual}/${anoAtual}`,
        mes: mesAtual.toString().padStart(2, '0'),
        ano: anoAtual.toString(),
      }]);
    }
  };



  // Memoizar o mês atual para evitar recálculos
  const mesAtualInfo = useMemo(() => {
    if (!extratoData) return { mes: 0, ano: 0 };
    return {
      mes: parseInt(extratoData.mes || '0'),
      ano: parseInt(extratoData.ano || '0')
    };
  }, [extratoData?.mes, extratoData?.ano]);

  // Verificar se é o mês atual
  const isMesAtual = (mesOption: MesOption) => {
    const mesOptionNum = parseInt(mesOption.mes);
    const anoOptionNum = parseInt(mesOption.ano);

    return mesOptionNum === mesAtualInfo.mes && anoOptionNum === mesAtualInfo.ano;
  };

  // Selecionar mês
  const selecionarMes = (mesOption: MesOption) => {
    setShowMesSelector(false);
    setMesSelecionado(mesOption.label); // Usar o label formatado (Mês/AA)
    loadExtratoData(mesOption.mes, mesOption.ano);
  };

  // Formatar valor no padrão brasileiro
  const formatarValor = (valor: string): string => {
    // Remove caracteres não numéricos exceto vírgula e ponto
    const numeroLimpo = valor.replace(/[^\d.,]/g, '');

    // Se já está no formato correto, retorna
    if (numeroLimpo.includes(',') && numeroLimpo.split(',')[1]?.length === 2) {
      return numeroLimpo;
    }

    // Converte para número e formata
    const numero = parseFloat(numeroLimpo.replace(',', '.'));
    if (isNaN(numero)) return valor;

    return numero.toLocaleString('pt-BR', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    });
  };

  // Função para determinar cor e sinal do valor baseado no tipo de vínculo
  const getValorFormatado = (item: any) => {
    const valorOriginal = parseFloat(item.valorParcela.replace(',', '.'));
    const tipoVinculo = item.tipoVinculo;

    // tipoVinculo "1" = Compra (deve ficar vermelho com sinal negativo)
    // tipoVinculo "2" = Pagamento/Crédito (deve ficar preto sem sinal)

    if (tipoVinculo === "2") {
      // Pagamento/Crédito: valor positivo, cor preta
      const valorPositivo = Math.abs(valorOriginal);
      return {
        valor: formatarValor(valorPositivo.toString().replace('.', ',')),
        cor: '#000000', // Preto
        sinal: '' // Sem sinal
      };
    } else {
      // Compra: valor negativo, cor vermelha
      const valorNegativo = Math.abs(valorOriginal);
      return {
        valor: formatarValor(valorNegativo.toString().replace('.', ',')),
        cor: '#FF4444', // Vermelho
        sinal: '-' // Com sinal negativo
      };
    }
  };

  // Filtrar itens não estornados (estornada = "0" ou undefined)
  const extratoFiltrado = useMemo(() => {
    if (!extratoData?.extrato) return [];

    return extratoData.extrato.filter(item => {
      // Mostrar apenas itens que não foram estornados
      // API retorna estornada como string: "0" = não estornado, "1" = estornado
      return !item.estornada || item.estornada === "0";
    });
  }, [extratoData?.extrato]);

  // Renderizar item do extrato
  const renderExtratoItem = ({ item, index }: { item: ExtratoItem; index: number }) => (
    <View style={[styles.extratoItem, index % 2 === 0 ? styles.evenRow : styles.oddRow]}>
      <View style={styles.itemData}>
        <Text style={styles.dataText}>{item.dataCompleta || item.data}</Text>
      </View>
      <View style={styles.itemParcela}>
        <Text style={styles.parcelaText}>{item.parcelas}</Text>
      </View>
      <TouchableOpacity
        style={styles.itemLoja}
        onPress={() => {
          const nomeCompleto = item.nomeEstabelecimento || item.loja;
          if (nomeCompleto && nomeCompleto.length > 15) {
            setTooltipText(nomeCompleto);
            setTooltipVisible(true);
          }
        }}
      >
        <Text style={styles.lojaText} numberOfLines={2} ellipsizeMode="tail">
          {item.nomeEstabelecimento || item.loja}
        </Text>
      </TouchableOpacity>
      <View style={styles.itemValor}>
        {(() => {
          const valorInfo = getValorFormatado(item);
          return (
            <Text style={[styles.valorText, { color: valorInfo.cor }]}>
              {valorInfo.sinal}{valorInfo.valor}
            </Text>
          );
        })()}
      </View>
    </View>
  );

  // Renderizar seletor de mês
  const renderMesSelector = () => (
    <View style={styles.mesSelectorContainer}>
      <Text style={styles.mesSelectorLabel}>Selecione o mês:</Text>
      <TouchableOpacity 
        style={styles.mesButton}
        onPress={() => setShowMesSelector(!showMesSelector)}
      >
        <Text style={styles.mesButtonText}>{mesSelecionado}</Text>
        <Text style={styles.mesButtonArrow}>▼</Text>
      </TouchableOpacity>
      
      {showMesSelector && (
        <View style={styles.mesDropdown}>
          <ScrollView style={styles.mesScrollView} nestedScrollEnabled>
            {mesesDisponiveis.map((mes, index) => (
              <TouchableOpacity
                key={index}
                style={styles.mesOption}
                onPress={() => selecionarMes(mes)}
              >
                <View style={styles.mesOptionContent}>
                  <Text style={styles.mesOptionText}>{mes.label}</Text>
                  {isMesAtual(mes) && <Text style={styles.mesAtualIcon}>●</Text>}
                </View>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>
      )}
    </View>
  );

  if (loading) {
    return (
      <View style={styles.container}>
        <SafeHeader
          title="Extrato"
          onBackPress={() => navigation.navigate('Home')}
        />

        <View style={styles.loadingContainer}>
          <LoadingSpinner message="Carregando extrato..." size="large" />
        </View>
      </View>
    );
  }

  if (!extratoData) {
    return (
      <View style={styles.container}>
        <SafeHeader
          title="Extrato"
          onBackPress={() => navigation.navigate('Home')}
        />

        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>Nenhum dado de extrato encontrado</Text>
          <TouchableOpacity
            style={styles.button}
            onPress={() => navigation.navigate('Home')}
          >
            <Text style={styles.buttonText}>Voltar</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <SafeHeader
        title="Extrato"
        onBackPress={() => navigation.navigate('Home')}
      />

      {/* Conteúdo */}
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Seletor de Mês */}
        {renderMesSelector()}

        {/* Cabeçalho da Lista */}
        <View style={styles.listHeader}>
          <View style={styles.headerLeft}>
            <Text style={styles.headerColumn}>Data</Text>
            <Text style={styles.headerColumn}>Parc.</Text>
            <Text style={styles.headerColumn}>Loja</Text>
          </View>
          <Text style={styles.headerColumnRight}>Valor R$</Text>
        </View>

        {/* Lista de Extratos */}
        <View style={styles.listContainer}>
          {extratoFiltrado && extratoFiltrado.length > 0 ? (
            <FlatList
              data={extratoFiltrado}
              renderItem={renderExtratoItem}
              keyExtractor={(item, index) => `${item.dataCompleta || item.data}-${index}`}
              scrollEnabled={false}
              ItemSeparatorComponent={() => <View style={styles.itemSeparator} />}
            />
          ) : (
            <View style={styles.emptyContainer}>
              <Text style={styles.emptyText}>Nenhum lançamento encontrado para este mês</Text>
            </View>
          )}
        </View>

        {/* Total */}
        <View style={[styles.totalContainer, { marginBottom: safeBottomPadding }]}>
          <Text style={styles.totalText}>Total - {extratoData?.total || 'R$ 0,00'}</Text>
        </View>
      </ScrollView>

      {/* Footer */}
      <Footer currentScreen="Extrato" navigation={navigation} />

      {/* Modal Tooltip */}
      <Modal
        visible={tooltipVisible}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setTooltipVisible(false)}
      >
        <TouchableOpacity
          style={styles.tooltipOverlay}
          onPress={() => setTooltipVisible(false)}
        >
          <View style={styles.tooltipContainer}>
            <Text style={styles.tooltipText}>{tooltipText}</Text>
          </View>
        </TouchableOpacity>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Cores.fundoPrincipal,
  },
  header: {
    backgroundColor: Cores.fundoHeader,
    paddingTop: 10,
    paddingBottom: 15,
    paddingHorizontal: 20,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  backButtonText: {
    color: '#FFF',
    fontSize: 20,
    fontWeight: 'bold',
  },
  headerTitle: {
    color: '#FFF',
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  placeholder: {
    width: 40,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 18,
    color: '#666',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 18,
    color: '#333',
    textAlign: 'center',
    marginBottom: 20,
  },
  button: {
    backgroundColor: Cores.primaria,
    borderRadius: 25,
    paddingVertical: 15,
    paddingHorizontal: 30,
    alignItems: 'center',
  },
  buttonText: {
    color: '#FFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
  mesSelectorContainer: {
    backgroundColor: '#FFF',
    borderRadius: 10,
    padding: 15,
    marginBottom: 20,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  mesSelectorLabel: {
    fontSize: 16,
    color: '#333',
    marginBottom: 10,
    fontWeight: '500',
  },
  mesButton: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#F8F8F8',
    borderRadius: 8,
    paddingHorizontal: 15,
    paddingVertical: 12,
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
  mesButtonText: {
    fontSize: 16,
    color: '#333',
  },
  mesButtonArrow: {
    fontSize: 12,
    color: '#666',
  },
  mesDropdown: {
    marginTop: 10,
    backgroundColor: '#FFF',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    maxHeight: 200,
  },
  mesScrollView: {
    maxHeight: 200,
  },
  mesOption: {
    paddingHorizontal: 15,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  mesOptionContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  mesOptionText: {
    fontSize: 16,
    color: '#333',
  },
  mesAtualIcon: {
    fontSize: 12,
    color: '#FF7F50',
    marginLeft: 10,
  },
  listHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 15,
    paddingVertical: 10,
    backgroundColor: '#E8E8E8',
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
  },
  headerLeft: {
    flexDirection: 'row',
    flex: 1,
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingRight: 10,
  },
  headerColumn: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
    flex: 1,
  },
  headerColumnRight: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
    textAlign: 'right',
    width: 80,
  },
  listContainer: {
    backgroundColor: '#FFF',
    borderBottomLeftRadius: 10,
    borderBottomRightRadius: 10,
    overflow: 'hidden',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  extratoItem: {
    flexDirection: 'row',
    paddingVertical: 12,
    paddingHorizontal: 15,
    backgroundColor: '#FFF',
    alignItems: 'center',
  },
  evenRow: {
    backgroundColor: '#FFF',
  },
  oddRow: {
    backgroundColor: '#F8F8F8',
  },
  itemData: {
    width: 80,
    alignItems: 'flex-start',
  },
  itemParcela: {
    width: 40,
    alignItems: 'center',
  },
  itemLoja: {
    flex: 1,
    paddingHorizontal: 10,
    alignItems: 'flex-start',
  },
  itemValor: {
    width: 80,
    alignItems: 'flex-end',
  },
  dataText: {
    fontSize: 12,
    color: '#333',
    fontWeight: '600',
  },
  parcelaText: {
    fontSize: 12,
    color: '#666',
    textAlign: 'center',
  },
  lojaText: {
    fontSize: 12,
    color: '#333',
    lineHeight: 14,
  },
  valorText: {
    fontSize: 14,
    color: '#FF4444',
    fontWeight: 'bold',
    textAlign: 'right',
  },
  totalContainer: {
    backgroundColor: '#FFF',
    borderRadius: 10,
    padding: 15,
    marginTop: 20,
    marginBottom: 20,
    alignItems: 'flex-end',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  totalText: {
    fontSize: 18,
    color: '#FF4444',
    fontWeight: 'bold',
  },
  itemSeparator: {
    height: 1,
    backgroundColor: '#F0F0F0',
  },
  emptyContainer: {
    padding: 30,
    alignItems: 'center',
    justifyContent: 'center',
  },
  emptyText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
  // Estilos do tooltip
  tooltipOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  tooltipContainer: {
    backgroundColor: 'white',
    padding: 20,
    borderRadius: 10,
    maxWidth: '80%',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
  },
  tooltipText: {
    fontSize: 16,
    color: Cores.textoEscuro,
    textAlign: 'center',
  },
});
