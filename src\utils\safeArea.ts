// Utilitários para Safe Area e insets do sistema
import { Platform, StatusBar, Dimensions } from 'react-native';
// import { useSafeAreaInsets } from 'react-native-safe-area-context';

// Obter dimensões da tela
const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

/**
 * Hook personalizado para obter insets seguros (implementação manual)
 */
export const useSafeInsets = () => {
  // Implementação manual sem react-native-safe-area-context
  const statusBarHeight = getStatusBarHeight();
  const navigationBarHeight = getNavigationBarHeight();

  return {
    top: statusBarHeight,
    bottom: navigationBarHeight,
    left: 0,
    right: 0,
  };
};

/**
 * Obter altura da StatusBar
 */
export const getStatusBarHeight = (): number => {
  if (Platform.OS === 'ios') {
    return 0; // iOS já é tratado pelo SafeAreaView
  }
  
  return StatusBar.currentHeight || 0;
};

/**
 * Obter altura da barra de navegação do sistema (Android)
 */
export const getNavigationBarHeight = (): number => {
  if (Platform.OS === 'ios') {
    return 0; // iOS não tem barra de navegação separada
  }

  // Estimativa baseada em densidade de tela
  const { height } = Dimensions.get('screen');
  const windowHeight = Dimensions.get('window').height;
  const statusBarHeight = getStatusBarHeight();

  // Diferença entre altura da tela e altura da janela (descontando status bar)
  const navigationBarHeight = height - windowHeight - statusBarHeight;

  // console.log('📱 Navigation Bar Debug:', {
  //   screenHeight: height,
  //   windowHeight: windowHeight,
  //   statusBarHeight: statusBarHeight,
  //   calculatedNavHeight: navigationBarHeight
  // });

  // Se a diferença for muito pequena, provavelmente não tem barra de navegação
  // Mas garantir um mínimo para dispositivos com gesture navigation
  if (navigationBarHeight <= 10) {
    return 10; // Padding mínimo aumentado para gesture navigation
  }

  // Limitar altura máxima para evitar valores absurdos
  return Math.min(Math.max(navigationBarHeight, 5), 10);
};

/**
 * Verificar se o dispositivo tem barra de navegação
 */
export const hasNavigationBar = (): boolean => {
  return getNavigationBarHeight() > 0;
};

/**
 * Obter padding bottom seguro para footer
 */
export const getSafeBottomPadding = (additionalPadding: number = 0): number => {
  const navigationBarHeight = getNavigationBarHeight();

  // console.log('📱 Safe Bottom Padding Debug:', {
  //   navigationBarHeight,
  //   additionalPadding,
  //   platform: Platform.OS
  // });

  // Para Android, sempre garantir padding suficiente
  if (Platform.OS === 'android') {
    // Se tem barra de navegação (altura > 20), adicionar padding extra
    if (navigationBarHeight > 20) {
      // Dispositivos com barra de navegação física - padding maior
      const totalPadding = navigationBarHeight;
      // console.log('📱 Android with nav bar:', totalPadding);
      return totalPadding;
    } else {
      // Dispositivos com gesture navigation - padding moderado
      const totalPadding = additionalPadding + 5; // Aumentado para gesture navigation
      // console.log('📱 Android with gestures:', totalPadding);
      return totalPadding;
    }
  } else {
    // iOS - padding menor
    return additionalPadding + 10;
  }
};

/**
 * Obter estilos seguros para container principal
 */
export const getSafeContainerStyles = () => ({
  paddingTop: getStatusBarHeight(),
  paddingBottom: getSafeBottomPadding(),
});

/**
 * Obter estilos seguros para footer
 */
export const getSafeFooterStyles = (basePadding: number = 12) => {
  const navigationBarHeight = getNavigationBarHeight();

  console.log('📱 Safe Footer Styles Debug:', {
    navigationBarHeight,
    basePadding,
    platform: Platform.OS
  });

  if (Platform.OS === 'android') {
    // Android: garantir padding suficiente para não sobrepor a barra de navegação
    const minimumBottomPadding = 20; // Aumentado para garantir espaço
    const calculatedPadding = navigationBarHeight > 20
      ? navigationBarHeight + basePadding + 10 // Com barra física
      : minimumBottomPadding + basePadding; // Com gestures

    const finalPadding = Math.max(calculatedPadding, minimumBottomPadding + basePadding);
    console.log('📱 Android footer padding:', finalPadding);

    return {
      paddingBottom: finalPadding
    };
  } else {
    // iOS: padding menor
    return {
      paddingBottom: basePadding + 10
    };
  }
};

/**
 * Verificar se é um dispositivo com notch/ilha dinâmica
 */
export const hasNotch = (): boolean => {
  if (Platform.OS === 'ios') {
    // Dispositivos iOS com notch têm altura específica
    return SCREEN_HEIGHT >= 812; // iPhone X e superiores
  }
  
  // Android: verificar se tem área de corte
  const statusBarHeight = getStatusBarHeight();
  return statusBarHeight > 24; // StatusBar padrão é 24dp
};

/**
 * Obter configurações responsivas considerando safe areas
 */
export const getResponsiveSafeStyles = () => {
  const statusBarHeight = getStatusBarHeight();
  const navigationBarHeight = getNavigationBarHeight();
  const hasNav = hasNavigationBar();
  
  return {
    // Container principal
    container: {
      paddingTop: statusBarHeight,
      paddingBottom: hasNav ? navigationBarHeight : 0,
    },
    
    // Header
    header: {
      paddingTop: hasNotch() ? 10 : 5,
    },
    
    // Footer
    footer: {
      paddingBottom: hasNav ? navigationBarHeight + 12 : 12,
    },
    
    // Scroll content
    scrollContent: {
      paddingBottom: hasNav ? navigationBarHeight + 20 : 20,
    },
  };
};

/**
 * Obter estilos para wrapper de Safe Area
 */
export const getSafeAreaWrapperStyles = (additionalStyle: any = {}) => {
  const safeStyles = getResponsiveSafeStyles();

  return {
    ...safeStyles.container,
    ...additionalStyle,
  };
};

/**
 * Informações detalhadas sobre safe areas
 */
export const getSafeAreaInfo = () => {
  const statusBarHeight = getStatusBarHeight();
  const navigationBarHeight = getNavigationBarHeight();
  
  return {
    platform: Platform.OS,
    statusBarHeight,
    navigationBarHeight,
    hasNavigationBar: hasNavigationBar(),
    hasNotch: hasNotch(),
    screenDimensions: {
      width: SCREEN_WIDTH,
      height: SCREEN_HEIGHT,
    },
    safeBottomPadding: getSafeBottomPadding(),
  };
};

/**
 * Hook para debug de safe areas
 */
export const useSafeAreaDebug = () => {
  const info = getSafeAreaInfo();
  
  console.log('📱 Safe Area Debug Info:', {
    platform: info.platform,
    statusBar: `${info.statusBarHeight}px`,
    navigationBar: `${info.navigationBarHeight}px`,
    hasNavBar: info.hasNavigationBar,
    hasNotch: info.hasNotch,
    screenSize: `${info.screenDimensions.width}x${info.screenDimensions.height}`,
    safeBottom: `${info.safeBottomPadding}px`,
  });
  
  return info;
};

/**
 * Constantes úteis
 */
export const SAFE_AREA_CONSTANTS = {
  MIN_NAVIGATION_BAR_HEIGHT: 48, // Altura mínima da barra de navegação
  DEFAULT_FOOTER_PADDING: 12,
  DEFAULT_HEADER_PADDING: 10,
  NOTCH_THRESHOLD_HEIGHT: 812, // Altura mínima para considerar notch
};

export default {
  useSafeInsets,
  getStatusBarHeight,
  getNavigationBarHeight,
  hasNavigationBar,
  getSafeBottomPadding,
  getSafeContainerStyles,
  getSafeFooterStyles,
  hasNotch,
  getResponsiveSafeStyles,
  getSafeAreaWrapperStyles,
  getSafeAreaInfo,
  useSafeAreaDebug,
  SAFE_AREA_CONSTANTS,
};
